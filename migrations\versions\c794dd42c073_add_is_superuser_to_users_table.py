"""Add is_superuser to users table

Revision ID: c794dd42c073
Revises: d146748c7f24
Create Date: 2025-07-14 17:50:25.864256

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c794dd42c073'
down_revision = 'd146748c7f24'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('audit_logs',
    sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('event_type', sa.String(), nullable=False),
    sa.Column('ip_address', sa.String(), nullable=True),
    sa.Column('details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_audit_logs_timestamp'), 'audit_logs', ['timestamp'], unique=False)
    op.drop_index(op.f('ix_oauth_applications_client_id'), table_name='oauth_applications')
    op.drop_index(op.f('ix_oauth_applications_id'), table_name='oauth_applications')
    op.drop_table('oauth_applications')
    op.add_column('applications', sa.Column('description', sa.Text(), nullable=True))
    op.add_column('applications', sa.Column('client_secret', sa.String(length=255), nullable=False))
    op.add_column('applications', sa.Column('redirect_uris', sa.Text(), nullable=False))
    op.add_column('applications', sa.Column('allowed_scopes', sa.Text(), nullable=False))
    op.add_column('applications', sa.Column('grant_types', sa.Text(), nullable=False))
    op.add_column('applications', sa.Column('response_types', sa.Text(), nullable=False))
    op.add_column('applications', sa.Column('is_confidential', sa.Boolean(), nullable=False))
    op.add_column('applications', sa.Column('require_consent', sa.Boolean(), nullable=False))
    op.add_column('applications', sa.Column('logo_url', sa.String(length=500), nullable=True))
    op.add_column('applications', sa.Column('website_url', sa.String(length=500), nullable=True))
    op.add_column('applications', sa.Column('privacy_policy_url', sa.String(length=500), nullable=True))
    op.add_column('applications', sa.Column('terms_of_service_url', sa.String(length=500), nullable=True))
    op.add_column('applications', sa.Column('token_endpoint_auth_method', sa.String(length=50), nullable=False))
    op.add_column('applications', sa.Column('access_token_lifetime', sa.Integer(), nullable=False))
    op.add_column('applications', sa.Column('refresh_token_lifetime', sa.Integer(), nullable=False))
    op.add_column('applications', sa.Column('authorization_code_lifetime', sa.Integer(), nullable=False))
    op.add_column('applications', sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('applications', sa.Column('created_by', sa.UUID(), nullable=False))
    op.alter_column('applications', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('true'))
    op.alter_column('applications', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.drop_constraint(op.f('applications_client_id_key'), 'applications', type_='unique')
    op.create_index(op.f('ix_applications_client_id'), 'applications', ['client_id'], unique=True)
    op.create_index(op.f('ix_applications_id'), 'applications', ['id'], unique=False)
    op.create_foreign_key(None, 'applications', 'users', ['created_by'], ['id'])
    op.drop_column('applications', 'client_secret_hash')
    op.alter_column('users', 'is_superuser',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('false'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'is_superuser',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.add_column('applications', sa.Column('client_secret_hash', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'applications', type_='foreignkey')
    op.drop_index(op.f('ix_applications_id'), table_name='applications')
    op.drop_index(op.f('ix_applications_client_id'), table_name='applications')
    op.create_unique_constraint(op.f('applications_client_id_key'), 'applications', ['client_id'], postgresql_nulls_not_distinct=False)
    op.alter_column('applications', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('applications', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('true'))
    op.drop_column('applications', 'created_by')
    op.drop_column('applications', 'updated_at')
    op.drop_column('applications', 'authorization_code_lifetime')
    op.drop_column('applications', 'refresh_token_lifetime')
    op.drop_column('applications', 'access_token_lifetime')
    op.drop_column('applications', 'token_endpoint_auth_method')
    op.drop_column('applications', 'terms_of_service_url')
    op.drop_column('applications', 'privacy_policy_url')
    op.drop_column('applications', 'website_url')
    op.drop_column('applications', 'logo_url')
    op.drop_column('applications', 'require_consent')
    op.drop_column('applications', 'is_confidential')
    op.drop_column('applications', 'response_types')
    op.drop_column('applications', 'grant_types')
    op.drop_column('applications', 'allowed_scopes')
    op.drop_column('applications', 'redirect_uris')
    op.drop_column('applications', 'client_secret')
    op.drop_column('applications', 'description')
    op.create_table('oauth_applications',
    sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('client_id', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('client_secret', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('redirect_uris', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('allowed_scopes', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('grant_types', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('response_types', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('is_confidential', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('require_consent', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('logo_url', sa.VARCHAR(length=500), autoincrement=False, nullable=True),
    sa.Column('website_url', sa.VARCHAR(length=500), autoincrement=False, nullable=True),
    sa.Column('privacy_policy_url', sa.VARCHAR(length=500), autoincrement=False, nullable=True),
    sa.Column('terms_of_service_url', sa.VARCHAR(length=500), autoincrement=False, nullable=True),
    sa.Column('token_endpoint_auth_method', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('access_token_lifetime', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('refresh_token_lifetime', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('authorization_code_lifetime', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('created_by', sa.UUID(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], name=op.f('oauth_applications_created_by_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('oauth_applications_pkey'))
    )
    op.create_index(op.f('ix_oauth_applications_id'), 'oauth_applications', ['id'], unique=False)
    op.create_index(op.f('ix_oauth_applications_client_id'), 'oauth_applications', ['client_id'], unique=True)
    op.drop_index(op.f('ix_audit_logs_timestamp'), table_name='audit_logs')
    op.drop_table('audit_logs')
    # ### end Alembic commands ###