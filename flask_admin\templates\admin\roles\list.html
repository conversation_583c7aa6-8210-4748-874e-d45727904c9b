{% extends "admin/base.html" %}

{% block title %}Roles Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">System Roles</h3>
                    <div>
                        <a href="{{ url_for('roles.list_permissions') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-key"></i> View Permissions
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if roles %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Role Name</th>
                                        <th>Description</th>
                                        <th>Permissions Count</th>
                                        <th>Users Count</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for role in roles %}
                                    <tr>
                                        <td>
                                            <strong>{{ role.role_name }}</strong>
                                        </td>
                                        <td>{{ role.description or 'No description' }}</td>
                                        <td>
                                            <span class="badge badge-primary">{{ role.permissions|length }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">{{ role.users|length }}</span>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('roles.view_role', role_id=role.id) }}" 
                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            No roles found. Please run the setup script to initialize roles and permissions.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Role Statistics -->
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ roles|length }}</h4>
                            <p class="mb-0">Total Roles</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users-cog fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ roles|sum(attribute='permissions')|length if roles else 0 }}</h4>
                            <p class="mb-0">Total Permissions</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-key fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ roles|sum(attribute='users')|length if roles else 0 }}</h4>
                            <p class="mb-0">Users with Roles</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.75em;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.btn-outline-primary:hover {
    transform: translateY(-1px);
    transition: all 0.2s;
}
</style>
{% endblock %}