{% extends "admin/base.html" %}

{% block title %}Permissions Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">System Permissions</h3>
                    <div>
                        <a href="{{ url_for('roles.list_roles') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-users-cog"></i> View Roles
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if permissions_by_category %}
                        {% for category, permissions in permissions_by_category.items() %}
                        <div class="permission-category mb-4">
                            <h5 class="category-title">
                                <i class="fas fa-folder-open text-primary"></i>
                                {{ category }}
                                <span class="badge badge-secondary ml-2">{{ permissions|length }}</span>
                            </h5>
                            <div class="row">
                                {% for permission in permissions %}
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="permission-card">
                                        <div class="permission-header">
                                            <strong>{{ permission.permission_name }}</strong>
                                        </div>
                                        <div class="permission-description">
                                            {{ permission.description or 'No description available' }}
                                        </div>
                                        <div class="permission-meta">
                                            <small class="text-muted">
                                                <i class="fas fa-tag"></i> {{ permission.category or 'Uncategorized' }}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        <hr>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            No permissions found. Please run the setup script to initialize roles and permissions.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Permission Statistics -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ permissions_by_category.values()|sum(attribute='__len__')|default(0) }}</h4>
                            <p class="mb-0">Total Permissions</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-key fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ permissions_by_category|length }}</h4>
                            <p class="mb-0">Categories</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-folder fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ (permissions_by_category.get('User Management', [])|length) }}</h4>
                            <p class="mb-0">User Permissions</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-cog fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ (permissions_by_category.get('System Administration', [])|length) }}</h4>
                            <p class="mb-0">Admin Permissions</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-cogs fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.permission-category {
    margin-bottom: 2rem;
}

.category-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.permission-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    height: 100%;
    transition: all 0.2s ease;
}

.permission-card:hover {
    background: #e9ecef;
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.15);
}

.permission-header {
    color: #007bff;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.permission-description {
    color: #6c757d;
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.permission-meta {
    border-top: 1px solid #dee2e6;
    padding-top: 0.5rem;
}

.badge {
    font-size: 0.75em;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

hr {
    border-top: 2px solid #e9ecef;
    margin: 2rem 0;
}
</style>
{% endblock %}