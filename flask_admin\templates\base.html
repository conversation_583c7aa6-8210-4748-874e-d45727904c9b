<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}SSO Admin Panel{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            /* Color Variables */
            --primary-color: #4361ee;
            --primary-light: #4895ef;
            --primary-dark: #3f37c9;
            --secondary-color: #4cc9f0;
            --success-color: #4ade80;
            --warning-color: #fbbf24;
            --danger-color: #f87171;
            --info-color: #60a5fa;
            --light-bg: #f8fafc;
            --dark-text: #1e293b;
            --light-text: #f8fafc;
            --muted-text: #64748b;
            --border-radius: 12px;
            --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            
            /* Spacing */
            --spacing-xs: 0.5rem;
            --spacing-sm: 0.75rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark-text);
            background-color: var(--light-bg);
            line-height: 1.6;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            box-shadow: var(--box-shadow);
            z-index: 1000;
            position: fixed;
            width: inherit;
            max-width: inherit;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.85);
            padding: var(--spacing-sm) var(--spacing-md);
            margin: 6px 0;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            border-left: 3px solid transparent;
        }
        
        .sidebar .nav-link i {
            font-size: 1.2rem;
            width: 24px;
            text-align: center;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
            border-left: 3px solid var(--light-text);
        }
        
        .sidebar .nav-link.active {
            color: var(--primary);
            background-color: rgba(var(--primary-rgb), 0.15);
            font-weight: 500;
            border-right: 3px solid var(--primary);
        }
        
        .sidebar.active {
            margin-left: -250px;
        }
        
        .main-content.active {
            margin-left: 0;
        }
        
        .main-content {
            background-color: var(--light-bg);
            min-height: 100vh;
            padding: var(--spacing-lg);
            margin-left: auto;
        }
        
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .stat-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--light-text);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            height: 100%;
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 30px -12px rgba(0, 0, 0, 0.15);
        }
        
        .stat-card-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%);
            color: var(--light-text);
        }
        
        .stat-card-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, var(--danger-color) 100%);
            color: var(--light-text);
        }
        
        .stat-card-info {
            background: linear-gradient(135deg, var(--info-color) 0%, var(--secondary-color) 100%);
            color: var(--light-text);
        }
        
        /* Icon Circle */
    .icon-circle {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      transition: transform 0.2s ease;
    }
    .icon-circle:hover {
      transform: scale(1.1);
    }
    .icon-circle i {
      font-size: 1.25rem;
    }
    
    /* Status Icon */
    .status-icon {
      font-size: 1.75rem;
      margin-bottom: 0.25rem;
    }
    
    /* Subtle Background Colors */
    .bg-primary-subtle {
      background-color: rgba(var(--bs-primary-rgb), 0.15);
    }
    .bg-success-subtle {
      background-color: rgba(var(--bs-success-rgb), 0.15);
    }
    .bg-info-subtle {
      background-color: rgba(var(--bs-info-rgb), 0.15);
    }
    .bg-warning-subtle {
      background-color: rgba(var(--bs-warning-rgb), 0.15);
    }
    .bg-danger-subtle {
      background-color: rgba(var(--bs-danger-rgb), 0.15);
    }
    .bg-secondary-subtle {
      background-color: rgba(var(--bs-secondary-rgb), 0.15);
    }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color);
        }
        
        .btn {
            border-radius: var(--border-radius);
            padding: 0.5rem 1.25rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: var(--primary-color);
            border: none;
            color: var(--light-text);
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
        }
        
        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: var(--light-text);
            transform: translateY(-2px);
        }
        
        .table {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }
        
        .table thead {
            background: var(--primary-color);
            color: var(--light-text);
        }
        
        .alert {
            border: none;
            border-radius: var(--border-radius);
            padding: 1rem 1.5rem;
        }
        
        .badge {
            padding: 0.5em 0.75em;
            font-weight: 500;
            border-radius: 30px;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 25px 25px;
        }

        /* Enhanced Navigation Styles */
        .sidebar .nav-link {
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 2px 0;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            font-weight: 600;
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 8px;
        }

        .sidebar .collapse .nav-link {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .sidebar .collapse .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .collapse .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
        }

        .sidebar-divider {
            border-color: rgba(255, 255, 255, 0.2);
        }

        .bi-chevron-down {
            transition: transform 0.3s ease;
        }

        .nav-link[aria-expanded="true"] .bi-chevron-down {
            transform: rotate(180deg);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            {% if session.token %}
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="d-flex align-items-center justify-content-center mb-4">
                        <div class="me-2">
                            <div class="avatar-circle bg-primary d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; border-radius: 50%;">
                                <span class="text-white fw-bold">{{ session.user.username[0] | upper if session.user.username else 'A' }}</span>
                            </div>
                        </div>
                        <div class="text-start">
                            <h5 class="text-white mb-0"><i class="bi bi-shield-lock"></i> SSO Admin</h5>
                            <small class="text-white-50">Welcome, {{ session.user.username or 'Admin' }}</small>
                        </div>
                    </div>
                    
                    <ul class="nav flex-column">
                        <!-- Dashboard -->
                        <li class="nav-item mb-1">
                            <a class="nav-link {% if request.endpoint == 'main.dashboard' %}active{% endif %}" href="{{ url_for('main.dashboard') }}">
                                <i class="bi bi-speedometer2"></i> Dashboard
                            </a>
                        </li>

                        <!-- User Management -->
                        <li class="nav-item mb-1">
                            <a class="nav-link d-flex align-items-center justify-content-between {% if request.endpoint in ['users.users', 'users.edit_user', 'users.user_profile'] %}active{% endif %}"
                               href="#userSubmenu" data-bs-toggle="collapse" aria-expanded="false">
                                <div>
                                    <i class="bi bi-people"></i> User Management
                                </div>
                                <i class="bi bi-chevron-down small"></i>
                            </a>
                            <div class="collapse {% if request.endpoint in ['users.users', 'users.edit_user', 'users.user_profile'] %}show{% endif %}" id="userSubmenu">
                                <ul class="nav flex-column ms-3 mt-1">
                                    <li class="nav-item mb-1">
                                        <a class="nav-link {% if request.endpoint == 'users.users' %}active{% endif %}" href="{{ url_for('users.users') }}">
                                            <i class="bi bi-list-ul"></i> All Users
                                        </a>
                                    </li>
                                    <li class="nav-item mb-1">
                                        <a class="nav-link {% if request.endpoint == 'users.edit_user' %}active{% endif %}" href="{{ url_for('users.edit_user', user_id=0) }}">
                                            <i class="bi bi-person-plus"></i> Create User
                                        </a>
                                    </li>
                                    <li class="nav-item mb-1">
                                        <a class="nav-link" href="#" onclick="showBulkActions('users')">
                                            <i class="bi bi-gear"></i> Bulk Actions
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Application Management -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint in ['applications.applications', 'applications.create_application', 'applications.edit_application'] %}active{% endif %}"
                               href="#appSubmenu" data-bs-toggle="collapse" aria-expanded="false">
                                <i class="bi bi-app"></i> Applications
                                <i class="bi bi-chevron-down float-end"></i>
                            </a>
                            <div class="collapse {% if request.endpoint in ['applications.applications', 'applications.create_application', 'applications.edit_application'] %}show{% endif %}" id="appSubmenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link {% if request.endpoint == 'applications.applications' %}active{% endif %}" href="{{ url_for('applications.applications') }}">
                                            <i class="bi bi-list-ul"></i> All Applications
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="showCreateApplicationModal()">
                                            <i class="bi bi-plus-circle"></i> Create Application
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="showBulkActions('applications')">
                                            <i class="bi bi-gear"></i> Bulk Actions
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Security & Access -->
                        <li class="nav-item mb-1">
                            <a class="nav-link d-flex align-items-center justify-content-between {% if request.endpoint in ['roles.roles', 'roles.permissions', 'roles.create_role', 'roles.edit_role', 'roles.create_permission'] %}active{% endif %}"
                               href="#securitySubmenu" data-bs-toggle="collapse" aria-expanded="false">
                                <div>
                                    <i class="bi bi-shield-lock"></i> Security & Access
                                </div>
                                <i class="bi bi-chevron-down small"></i>
                            </a>
                            <div class="collapse {% if request.endpoint in ['roles.roles', 'roles.permissions', 'roles.create_role', 'roles.edit_role', 'roles.create_permission'] %}show{% endif %}" id="securitySubmenu">
                                <ul class="nav flex-column ms-3 mt-1">
                                    <li class="nav-item mb-1">
                                        <a class="nav-link {% if request.endpoint == 'roles.roles' %}active{% endif %}" href="{{ url_for('roles.roles') }}">
                                            <i class="bi bi-person-badge"></i> Roles
                                        </a>
                                    </li>
                                    <li class="nav-item mb-1">
                                        <a class="nav-link {% if request.endpoint == 'roles.permissions' %}active{% endif %}" href="{{ url_for('roles.permissions') }}">
                                            <i class="bi bi-key"></i> Permissions
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Organization Structure -->
                        <li class="nav-item mb-1">
                            <a class="nav-link d-flex align-items-center justify-content-between {% if request.endpoint in ['organization.branches', 'organization.departments', 'organization.positions'] %}active{% endif %}"
                               href="#orgSubmenu" data-bs-toggle="collapse" aria-expanded="false">
                                <div>
                                    <i class="bi bi-building"></i> Organization
                                </div>
                                <i class="bi bi-chevron-down small"></i>
                            </a>
                            <div class="collapse {% if request.endpoint in ['organization.branches', 'organization.departments', 'organization.positions'] %}show{% endif %}" id="orgSubmenu">
                                <ul class="nav flex-column ms-3 mt-1">
                                    <li class="nav-item mb-1">
                                        <a class="nav-link {% if request.endpoint == 'organization.branches' %}active{% endif %}" href="{{ url_for('organization.branches') }}">
                                            <i class="bi bi-geo-alt"></i> Branches
                                        </a>
                                    </li>
                                    <li class="nav-item mb-1">
                                        <a class="nav-link {% if request.endpoint == 'organization.departments' %}active{% endif %}" href="{{ url_for('organization.departments') }}">
                                            <i class="bi bi-diagram-3"></i> Departments
                                        </a>
                                    </li>
                                    <li class="nav-item mb-1">
                                        <a class="nav-link {% if request.endpoint == 'organization.positions' %}active{% endif %}" href="{{ url_for('organization.positions') }}">
                                            <i class="bi bi-briefcase"></i> Positions
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Analytics & Reports -->
                        <li class="nav-item mb-1">
                            <a class="nav-link d-flex align-items-center justify-content-between {% if request.endpoint in ['analytics', 'reports', 'system_stats', 'user_stats'] %}active{% endif %}"
                               href="#analyticsSubmenu" data-bs-toggle="collapse" aria-expanded="false">
                                <div>
                                    <i class="bi bi-graph-up"></i> Analytics & Reports
                                </div>
                                <i class="bi bi-chevron-down small"></i>
                            </a>
                            <div class="collapse {% if request.endpoint in ['analytics', 'reports', 'system_stats', 'user_stats'] %}show{% endif %}" id="analyticsSubmenu">
                                <ul class="nav flex-column ms-3 mt-1">
                                    <li class="nav-item mb-1">
                                        <a class="nav-link {% if request.endpoint == 'system_stats' %}active{% endif %}" href="#" onclick="loadPage('system_stats')">
                                            <i class="bi bi-bar-chart"></i> System Statistics
                                        </a>
                                    </li>
                                    <li class="nav-item mb-1">
                                        <a class="nav-link {% if request.endpoint == 'user_stats' %}active{% endif %}" href="#" onclick="loadPage('user_stats')">
                                            <i class="bi bi-people-fill"></i> User Analytics
                                        </a>
                                    </li>
                                    <li class="nav-item mb-1">
                                        <a class="nav-link {% if request.endpoint == 'activities' %}active{% endif %}" href="#" onclick="loadPage('activities')">
                                            <i class="bi bi-clock-history"></i> Activity Logs
                                        </a>
                                    </li>
                                    <li class="nav-item mb-1">
                                        <a class="nav-link" href="#" onclick="generateReport()">
                                            <i class="bi bi-file-earmark-text"></i> Generate Report
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- System Settings -->
                        <li class="nav-item mb-1">
                            <a class="nav-link d-flex align-items-center justify-content-between {% if request.endpoint in ['settings', 'system_config', 'oauth_settings'] %}active{% endif %}"
                               href="#settingsSubmenu" data-bs-toggle="collapse" aria-expanded="false">
                                <div>
                                    <i class="bi bi-gear"></i> System Settings
                                </div>
                                <i class="bi bi-chevron-down small"></i>
                            </a>
                            <div class="collapse {% if request.endpoint in ['settings', 'system_config', 'oauth_settings'] %}show{% endif %}" id="settingsSubmenu">
                                <ul class="nav flex-column ms-3 mt-1">
                                    <li class="nav-item mb-1">
                                        <a class="nav-link {% if request.endpoint == 'system_config' %}active{% endif %}" href="#" onclick="loadPage('system_config')">
                                            <i class="bi bi-sliders"></i> System Configuration
                                        </a>
                                    </li>
                                    <li class="nav-item mb-1">
                                        <a class="nav-link {% if request.endpoint == 'oauth_settings' %}active{% endif %}" href="#" onclick="loadPage('oauth_settings')">
                                            <i class="bi bi-shield-check"></i> OAuth Settings
                                        </a>
                                    </li>
                                    <li class="nav-item mb-1">
                                        <a class="nav-link" href="#" onclick="showSystemHealth()">
                                            <i class="bi bi-heart-pulse"></i> System Health
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Quick Access -->
                        <li class="nav-item mb-1">
                            <a class="nav-link d-flex align-items-center justify-content-between"
                               href="#quickAccessSubmenu" data-bs-toggle="collapse" aria-expanded="false">
                                <div>
                                    <i class="bi bi-lightning"></i> Quick Access
                                </div>
                                <i class="bi bi-chevron-down small"></i>
                            </a>
                            <div class="collapse" id="quickAccessSubmenu">
                                <ul class="nav flex-column ms-3 mt-1">
                                    <li class="nav-item mb-1">
                                        <a class="nav-link" href="{{ url_for('organization.departments') }}">
                                            <i class="bi bi-diagram-3"></i> Departments
                                        </a>
                                    </li>
                                    <li class="nav-item mb-1">
                                        <a class="nav-link" href="{{ url_for('organization.branches') }}">
                                            <i class="bi bi-geo-alt"></i> Branches
                                        </a>
                                    </li>
                                    <li class="nav-item mb-1">
                                        <a class="nav-link" href="{{ url_for('organization.positions') }}">
                                            <i class="bi bi-briefcase"></i> Positions
                                        </a>
                                    </li>
                                    <li class="nav-item mb-1">
                                        <a class="nav-link" href="{{ url_for('users.users') }}">
                                            <i class="bi bi-people"></i> All Users
                                        </a>
                                    </li>
                                    <li class="nav-item mb-1">
                                        <a class="nav-link" href="{{ url_for('applications.applications') }}">
                                            <i class="bi bi-app"></i> All Applications
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Divider -->
                        <hr class="sidebar-divider my-3">

                        <hr class="my-3 border-secondary opacity-25">
                         
                         <!-- User Account -->
                         <li class="nav-item mb-1">
                             <a class="nav-link {% if request.endpoint == 'profile' %}active{% endif %}" href="#" onclick="loadPage('profile')">
                                 <i class="bi bi-person-circle"></i> My Profile
                             </a>
                         </li>

                         <!-- Logout -->
                         <li class="nav-item mb-1">
                             <a class="nav-link" href="{{ url_for('main.logout') }}">
                                 <i class="bi bi-box-arrow-right"></i> Logout
                             </a>
                         </li>
                    </ul>
                </div>
            </nav>
            {% endif %}
            
            <!-- Main content -->
            <main class="{% if session.token %}col-md-9 ms-sm-auto col-lg-10 px-md-4{% else %}col-12{% endif %} main-content">
                {% if session.token %}
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4">
                    <div>
                        <h1 class="h2 mb-0" id="page-title">Dashboard</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="#" class="text-decoration-none">Home</a></li>
                                <li class="breadcrumb-item active" aria-current="page" id="breadcrumb-current">Dashboard</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="btn-toolbar mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-outline-primary" onclick="generateReport()">
                                <i class="bi bi-download me-1"></i> Export
                            </button>
                            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#systemHealthModal">
                                <i class="bi bi-heart-pulse me-1"></i> System Health
                            </button>
                        </div>
                    </div>
                </div>
                {% elif not session.token %}
                <div class="page-header text-center">
                    <h1><i class="bi bi-shield-lock"></i> SSO Admin Panel</h1>
                    <p class="lead">Secure Single Sign-On Administration</p>
                </div>
                {% endif %}
                
                <!-- Flash messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="mt-3">
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    <i class="bi bi-{% if category == 'success' %}check-circle{% elif category == 'error' %}exclamation-triangle{% elif category == 'warning' %}exclamation-circle{% else %}info-circle{% endif %}"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}
                
                <!-- Page content -->
                <div id="page-content" class="pb-4">
                      {% block content %}{% endblock %}
                  </div>
              </main>
          </div>
      </div>
  
  <!-- System Health Modal -->
  <div class="modal fade" id="systemHealthModal" tabindex="-1" aria-labelledby="systemHealthModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="systemHealthModalLabel">
            <i class="bi bi-activity me-2"></i>System Health Dashboard
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <div class="card mb-3">
                <div class="card-header py-3">
                  <h6 class="mb-0 fw-semibold">Server Status</h6>
                </div>
                <div class="card-body p-0">
                  <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center py-3">
                      <div>
                        <i class="bi bi-cpu icon-circle bg-success-subtle"></i>
                        <span class="ms-2">CPU Usage</span>
                      </div>
                      <span class="badge bg-success">12%</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center py-3">
                      <div>
                        <i class="bi bi-memory icon-circle bg-info-subtle"></i>
                        <span class="ms-2">Memory Usage</span>
                      </div>
                      <span class="badge bg-info">34%</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center py-3">
                      <div>
                        <i class="bi bi-hdd icon-circle bg-primary-subtle"></i>
                        <span class="ms-2">Disk Space</span>
                      </div>
                      <span class="badge bg-primary">67%</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center py-3">
                      <div>
                        <i class="bi bi-arrow-up-circle icon-circle bg-success-subtle"></i>
                        <span class="ms-2">Uptime</span>
                      </div>
                      <span>14 days, 3 hours</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card mb-3">
                <div class="card-header py-3">
                  <h6 class="mb-0 fw-semibold">Service Status</h6>
                </div>
                <div class="card-body p-0">
                  <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center py-3">
                      <div>
                        <i class="bi bi-database-check icon-circle bg-success-subtle"></i>
                        <span class="ms-2">Database</span>
                      </div>
                      <span class="badge bg-success">Operational</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center py-3">
                      <div>
                        <i class="bi bi-envelope-check icon-circle bg-success-subtle"></i>
                        <span class="ms-2">Email Service</span>
                      </div>
                      <span class="badge bg-success">Operational</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center py-3">
                      <div>
                        <i class="bi bi-cloud-check icon-circle bg-success-subtle"></i>
                        <span class="ms-2">API Gateway</span>
                      </div>
                      <span class="badge bg-success">Operational</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center py-3">
                      <div>
                        <i class="bi bi-shield-check icon-circle bg-warning-subtle"></i>
                        <span class="ms-2">Authentication</span>
                      </div>
                      <span class="badge bg-warning">Degraded</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-12">
              <div class="card">
                <div class="card-header py-3">
                  <h6 class="mb-0 fw-semibold">Recent System Logs</h6>
                </div>
                <div class="card-body p-0">
                  <div class="table-responsive">
                    <table class="table table-hover mb-0">
                      <thead>
                        <tr>
                          <th>Timestamp</th>
                          <th>Level</th>
                          <th>Service</th>
                          <th>Message</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td class="text-nowrap">2023-06-15 14:32:10</td>
                          <td><span class="badge bg-warning">WARNING</span></td>
                          <td>Authentication</td>
                          <td>High rate of failed login attempts detected</td>
                        </tr>
                        <tr>
                          <td class="text-nowrap">2023-06-15 14:30:05</td>
                          <td><span class="badge bg-success">INFO</span></td>
                          <td>User Service</td>
                          <td>User profile batch update completed successfully</td>
                        </tr>
                        <tr>
                          <td class="text-nowrap">2023-06-15 14:28:33</td>
                          <td><span class="badge bg-info">DEBUG</span></td>
                          <td>API Gateway</td>
                          <td>Rate limiting applied to client 192.168.1.105</td>
                        </tr>
                        <tr>
                          <td class="text-nowrap">2023-06-15 14:25:17</td>
                          <td><span class="badge bg-danger">ERROR</span></td>
                          <td>Database</td>
                          <td>Connection pool exhausted, increasing capacity</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <button type="button" class="btn btn-primary">Download Full Report</button>
        </div>
      </div>
    </div>
  </div>          </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarCollapse');
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    document.querySelector('.sidebar').classList.toggle('active');
                    document.querySelector('.main-content').classList.toggle('active');
                });
            }
        });
        
        // Confirm delete actions
        document.addEventListener('DOMContentLoaded', function() {
            const deleteButtons = document.querySelectorAll('[data-confirm-delete]');
            deleteButtons.forEach(function(button) {
                button.addEventListener('click', function(e) {
                    if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                        e.preventDefault();
                    }
                });
            });
            
            // Initialize all tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // Initialize all popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
            
            // Dropdown toggle functionality for sidebar menu items
            const navLinks = document.querySelectorAll('.sidebar .nav-link[data-bs-toggle="collapse"]');
            navLinks.forEach(function(link) {
                link.addEventListener('click', function() {
                    const chevronIcon = this.querySelector('.bi-chevron-down');
                    if (chevronIcon) {
                        // The rotation is handled by CSS transition in the existing styles
                    }
                });
            });
            
            // Set active menu item based on current URL
            const currentPath = window.location.pathname;
            const sidebarLinks = document.querySelectorAll('.sidebar .nav-link');
            sidebarLinks.forEach(function(link) {
                const href = link.getAttribute('href');
                if (href && currentPath.includes(href) && href !== '#') {
                    link.classList.add('active');
                    
                    // If inside a submenu, expand the parent
                    const parentCollapse = link.closest('.collapse');
                    if (parentCollapse) {
                        new bootstrap.Collapse(parentCollapse).show();
                    }
                }
            });
        });

        // Enhanced Navigation Functions
        function loadPage(page) {
            // Show loading indicator
            showLoadingIndicator();

            // Map page names to actual routes
            const pageRoutes = {
                'branches': '/organization/branches',
                'departments': '/organization/departments',
                'positions': '/organization/positions',
                'system_stats': '/analytics/system-stats',
                'user_stats': '/analytics/user-stats',
                'activities': '/analytics/activities',
                'system_config': '/settings/system',
                'oauth_settings': '/settings/oauth',
                'profile': '/profile'
            };

            const route = pageRoutes[page];
            if (route) {
                window.location.href = route;
            } else {
                // For now, show a placeholder message
                showPlaceholderMessage(page);
            }
        }

        function showPlaceholderMessage(page) {
            hideLoadingIndicator();
            const pageName = page.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
            alert(`${pageName} page is coming soon! This feature will be implemented in the next update.`);
        }

        function showBulkActions(type) {
            const actionType = type === 'users' ? 'User' : 'Application';
            const actions = type === 'users'
                ? ['Activate', 'Deactivate', 'Verify', 'Delete', 'Unlock']
                : ['Activate', 'Deactivate', 'Delete'];

            let actionList = actions.map(action => `<option value="${action.toLowerCase()}">${action}</option>`).join('');

            const modalHtml = `
                <div class="modal fade" id="bulkActionModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Bulk ${actionType} Actions</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>Select an action to perform on multiple ${type}:</p>
                                <select class="form-select" id="bulkActionSelect">
                                    <option value="">Choose an action...</option>
                                    ${actionList}
                                </select>
                                <div class="mt-3">
                                    <small class="text-muted">Note: You'll be able to select specific ${type} after choosing an action.</small>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" onclick="executeBulkAction('${type}')">Continue</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('bulkActionModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('bulkActionModal'));
            modal.show();
        }

        function executeBulkAction(type) {
            const action = document.getElementById('bulkActionSelect').value;
            if (!action) {
                alert('Please select an action first.');
                return;
            }

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('bulkActionModal'));
            modal.hide();

            // Navigate to the appropriate page with bulk action mode
            const targetPage = type === 'users' ? '/users' : '/applications';
            window.location.href = `${targetPage}?bulk_action=${action}`;
        }

        function showCreateApplicationModal() {
            // For now, redirect to applications page
            // In the future, this could open a modal for quick app creation
            window.location.href = '/applications';
        }

        function generateReport() {
            showLoadingIndicator();

            // Simulate report generation
            setTimeout(() => {
                hideLoadingIndicator();
                alert('Report generation feature is coming soon! This will allow you to export system data and analytics.');
            }, 1000);
        }

        function showSystemHealth() {
            showLoadingIndicator();

            // Make API call to check system health
            fetch('/api/health')
                .then(response => response.json())
                .then(data => {
                    hideLoadingIndicator();
                    showSystemHealthModal(data);
                })
                .catch(error => {
                    hideLoadingIndicator();
                    console.error('Error fetching system health:', error);
                    alert('Error checking system health. Please try again.');
                });
        }

        function showSystemHealthModal(healthData) {
            const statusIcon = healthData.status === 'healthy' ? '✅' : '❌';
            const statusClass = healthData.status === 'healthy' ? 'text-success' : 'text-danger';

            const modalHtml = `
                <div class="modal fade" id="systemHealthModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">System Health Status</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center mb-3">
                                    <h3 class="${statusClass}">${statusIcon} ${healthData.status.toUpperCase()}</h3>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <strong>Database:</strong>
                                        <span class="${healthData.database === 'healthy' ? 'text-success' : 'text-danger'}">
                                            ${healthData.database}
                                        </span>
                                    </div>
                                    <div class="col-6">
                                        <strong>Cache:</strong>
                                        <span class="${healthData.cache === 'healthy' ? 'text-success' : 'text-danger'}">
                                            ${healthData.cache}
                                        </span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <strong>Version:</strong> ${healthData.version}
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="button" class="btn btn-primary" onclick="location.reload()">Refresh</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('systemHealthModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('systemHealthModal'));
            modal.show();
        }

        function showLoadingIndicator() {
            // Create or show loading indicator
            let loader = document.getElementById('globalLoader');
            if (!loader) {
                loader = document.createElement('div');
                loader.id = 'globalLoader';
                loader.innerHTML = `
                    <div class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
                         style="background: rgba(0,0,0,0.5); z-index: 9999;">
                        <div class="spinner-border text-light" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                `;
                document.body.appendChild(loader);
            }
            loader.style.display = 'block';
        }

        function hideLoadingIndicator() {
            const loader = document.getElementById('globalLoader');
            if (loader) {
                loader.style.display = 'none';
            }
        }
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>