{% extends "base.html" %}

{% block title %}User Management - SSO Admin Panel{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-people"></i> User Management</h1>
    {% if not appadmin_readonly %}
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('users.edit_user', user_id=0) }}" class="btn btn-primary">
                <i class="bi bi-person-plus"></i> Create User
            </a>
        </div>
    </div>
    {% endif %}
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search Users</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="Username, email, or full name">
            </div>
            <div class="col-md-3">
                <label for="is_active" class="form-label">Status</label>
                <select class="form-select" id="is_active" name="is_active">
                    <option value="">All Users</option>
                    <option value="true" {% if is_active == 'true' %}selected{% endif %}>Active Only</option>
                    <option value="false" {% if is_active == 'false' %}selected{% endif %}>Inactive Only</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="bi bi-search"></i> Search
                </button>
                <a href="{{ url_for('users.users') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise"></i> Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Users ({{ data.total if data.total else data.users|length }})</h5>
        {% if not appadmin_readonly %}
        <div class="btn-group btn-group-sm" role="group">
            <button type="button" class="btn btn-outline-success" onclick="bulkAction('activate')" id="bulk-activate" disabled>
                <i class="bi bi-check-circle"></i> Activate
            </button>
            <button type="button" class="btn btn-outline-warning" onclick="bulkAction('deactivate')" id="bulk-deactivate" disabled>
                <i class="bi bi-x-circle"></i> Deactivate
            </button>
            <button type="button" class="btn btn-outline-danger" onclick="bulkAction('delete')" id="bulk-delete" disabled>
                <i class="bi bi-trash"></i> Delete
            </button>
        </div>
        {% endif %}
    </div>
    <div class="card-body p-0">
        {% if data.users %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        {% if not appadmin_readonly %}
                        <th width="50">
                            <input type="checkbox" id="select-all" class="form-check-input">
                        </th>
                        {% endif %}
                        <th>User</th>
                        <th>Full Name</th>
                        <th>Branch</th>
                        <th>Department</th>
                        <th>Position</th>
                        <th>Manager</th>
                        <th>Permissions</th>
                        <th>Email</th>
                        <th>Status</th>
                        <th>Role</th>
                        <th>Last Login</th>
                        <th>Created</th>
                        <th width="150">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in data.users %}
                    <tr class="user-row" {% if not appadmin_readonly %}style="cursor:pointer;" onclick="window.location='{{ url_for('users.edit_user', user_id=user.id) }}'"{% endif %}>
                        {% if not appadmin_readonly %}
                        <td>
                            <input type="checkbox" class="form-check-input user-checkbox" value="{{ user.id }}">
                        </td>
                        {% endif %}
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <div class="avatar-title rounded-circle bg-primary text-white">
                                        {{ user.username[0].upper() if user.username else 'U' }}
                                    </div>
                                </div>
                                <div>
                                    <div class="fw-semibold">{{ user.username }}</div>
                                </div>
                            </div>
                        </td>
                        <td>{{ user.full_name }}</td>
                        <td>{{ user.branch }}</td>
                        <td>{{ user.department }}</td>
                        <td>{{ user.position }}</td>
                        <td>{{ user.manager_name or '-' }}</td>
                        <td>
                            {% if user.effective_permissions %}
                                {% set perms = user.effective_permissions %}
                                {% set preview = perms[:2] %}
                                <span title="{{ perms|join(', ') }}">
                                    {{ preview|join(', ') }}
                                    {% if perms|length > 2 %}
                                        +{{ perms|length - 2 }} more
                                    {% endif %}
                                </span>
                            {% else %}
                                <span class="text-muted">None</span>
                            {% endif %}
                        </td>
                        <td>
                            {{ user.email }}
                            {% if user.is_verified %}
                                <span class="badge bg-success ms-1">Verified</span>
                            {% else %}
                                <span class="badge bg-secondary ms-1">Not Verified</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_active %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-secondary">Inactive</span>
                            {% endif %}
                            {% if user.is_locked %}
                                <span class="badge bg-warning">Locked</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_superuser %}
                                <span class="badge bg-primary">Admin</span>
                            {% else %}
                                <span class="badge bg-secondary">User</span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">{{ user.last_login if user.last_login else 'Never' }}</small>
                        </td>
                        <td>
                            <small class="text-muted">{{ user.created_at if user.created_at else 'Unknown' }}</small>
                        </td>
                        <td>
                            {% if appadmin_readonly %}
                                <form method="POST" action="#" class="d-flex align-items-center">
                                    <input type="hidden" name="user_id" value="{{ user.id }}">
                                    <select name="role_id" class="form-select form-select-sm me-2" style="width:auto;">
                                        {% for role in roles %}
                                            <option value="{{ role.id }}" {% if user.role == role.name %}selected{% endif %}>{{ role.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <button type="submit" class="btn btn-outline-primary btn-sm">Assign</button>
                                </form>
                            {% else %}
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('users.edit_user', user_id=user.id) }}"
                                   class="btn btn-outline-primary" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                {% if not user.is_superuser or user.id != session.user.id %}
                                <form method="POST" action="{{ url_for('users.delete_user', user_id=user.id) }}"
                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this user?')">
                                    <button type="submit" class="btn btn-outline-danger" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <!-- Pagination -->
        {% if data.pages > 1 %}
        <div class="card-footer">
            <nav aria-label="User pagination">
                <ul class="pagination pagination-sm justify-content-center mb-0">
                    {% if data.page > 1 %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('users.users', page=data.page-1, search=search, is_active=is_active) }}">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                    {% endif %}
                    {% for page_num in range(1, data.pages + 1) %}
                        {% if page_num == data.page %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% elif page_num <= 3 or page_num > data.pages - 3 or (page_num >= data.page - 1 and page_num <= data.page + 1) %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('users.users', page=page_num, search=search, is_active=is_active) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                        {% elif page_num == 4 or page_num == data.pages - 3 %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {% endif %}
                    {% endfor %}
                    {% if data.page < data.pages %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('users.users', page=data.page+1, search=search, is_active=is_active) }}">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-people" style="font-size: 4rem; color: #dee2e6;"></i>
            <h4 class="mt-3 text-muted">No Users Found</h4>
            <p class="text-muted">{% if search %}No users match your search criteria.{% else %}No users have been created yet.{% endif %}</p>
            {% if not appadmin_readonly %}
            <a href="{{ url_for('users.edit_user', user_id=0) }}" class="btn btn-primary">
                <i class="bi bi-person-plus"></i> Create First User
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
{% if not appadmin_readonly %}
<script>
    // Select all checkbox functionality
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.user-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActionButtons();
    });
    // Individual checkbox change
    document.querySelectorAll('.user-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionButtons);
    });
    function updateBulkActionButtons() {
        const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
        const bulkButtons = document.querySelectorAll('[id^="bulk-"]');
        bulkButtons.forEach(button => {
            button.disabled = checkedBoxes.length === 0;
        });
        // Update select-all checkbox state
        const allCheckboxes = document.querySelectorAll('.user-checkbox');
        const selectAllCheckbox = document.getElementById('select-all');
        if (checkedBoxes.length === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedBoxes.length === allCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }
    function bulkAction(action) {
        const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
        const userIds = Array.from(checkedBoxes).map(cb => cb.value);
        if (userIds.length === 0) {
            alert('Please select at least one user.');
            return;
        }
        const actionText = action === 'delete' ? 'delete' : action;
        if (!confirm(`Are you sure you want to ${actionText} ${userIds.length} user(s)?`)) {
            return;
        }
        // Show loading state
        const button = document.getElementById(`bulk-${action}`);
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';
        button.disabled = true;
        // Make API call
        fetch('/api/bulk-user-action', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_ids: userIds,
                action: action
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.error || 'Unknown error occurred'));
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
    // Auto-submit search form on Enter
    document.getElementById('search').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            this.form.submit();
        }
    });
</script>
{% endif %}
<style>
    .avatar-sm {
        width: 40px;
        height: 40px;
    }
    .avatar-title {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
    }
    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
    }
</style>
{% endblock %}