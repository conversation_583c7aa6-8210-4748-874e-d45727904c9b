{% extends "admin/base.html" %}

{% block title %}Role Details - {{ role.role_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-user-tag text-primary"></i>
                        Role: {{ role.role_name }}
                    </h3>
                    <div>
                        <a href="{{ url_for('roles.list_roles') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Roles
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Role Information -->
                        <div class="col-md-6">
                            <div class="info-section">
                                <h5 class="section-title">
                                    <i class="fas fa-info-circle text-info"></i>
                                    Role Information
                                </h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Name:</strong></td>
                                        <td>{{ role.role_name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Description:</strong></td>
                                        <td>{{ role.description or 'No description provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Created:</strong></td>
                                        <td>{{ role.created_at.strftime('%Y-%m-%d %H:%M') if role.created_at else 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Users Count:</strong></td>
                                        <td>
                                            <span class="badge badge-info">{{ users_with_role|length }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Permissions Count:</strong></td>
                                        <td>
                                            <span class="badge badge-primary">{{ role.permissions|length }}</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Users with this Role -->
                        <div class="col-md-6">
                            <div class="info-section">
                                <h5 class="section-title">
                                    <i class="fas fa-users text-success"></i>
                                    Users with this Role
                                </h5>
                                {% if users_with_role %}
                                    <div class="users-list">
                                        {% for user in users_with_role %}
                                        <div class="user-item">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong>{{ user.username }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ user.email }}</small>
                                                    {% if user.is_superuser %}
                                                        <span class="badge badge-danger ml-2">Super Admin</span>
                                                    {% endif %}
                                                </div>
                                                <div>
                                                    <a href="{{ url_for('roles.manage_user_roles', user_id=user.id) }}" 
                                                       class="btn btn-sm btn-outline-primary" title="Manage Roles">
                                                        <i class="fas fa-cog"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        No users currently have this role.
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Role Permissions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="info-section">
                                <h5 class="section-title">
                                    <i class="fas fa-key text-warning"></i>
                                    Role Permissions
                                </h5>
                                {% if role.permissions %}
                                    {% set permissions_by_category = {} %}
                                    {% for permission in role.permissions %}
                                        {% set category = permission.category or 'Other' %}
                                        {% if category not in permissions_by_category %}
                                            {% set _ = permissions_by_category.update({category: []}) %}
                                        {% endif %}
                                        {% set _ = permissions_by_category[category].append(permission) %}
                                    {% endfor %}

                                    {% for category, permissions in permissions_by_category.items() %}
                                    <div class="permission-category mb-3">
                                        <h6 class="category-header">
                                            <i class="fas fa-folder text-primary"></i>
                                            {{ category }}
                                            <span class="badge badge-secondary ml-2">{{ permissions|length }}</span>
                                        </h6>
                                        <div class="row">
                                            {% for permission in permissions %}
                                            <div class="col-md-4 col-lg-3 mb-2">
                                                <div class="permission-badge">
                                                    <i class="fas fa-check-circle text-success"></i>
                                                    {{ permission.permission_name }}
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        This role has no permissions assigned.
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-section {
    background: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.user-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.user-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.15);
}

.users-list {
    max-height: 300px;
    overflow-y: auto;
}

.permission-category {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
}

.category-header {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.permission-badge {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 0.25rem;
    padding: 0.5rem;
    font-size: 0.85rem;
    color: #0056b3;
    text-align: center;
}

.badge {
    font-size: 0.75em;
}

.table-borderless td {
    border: none;
    padding: 0.5rem 0;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.btn-outline-primary:hover {
    transform: translateY(-1px);
    transition: all 0.2s;
}
</style>
{% endblock %}