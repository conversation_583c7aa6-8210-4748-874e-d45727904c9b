{% extends "base.html" %}

{% block title %}{{ title }} - SSO Admin Panel{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-{% if user %}pencil{% else %}person-plus{% endif %}"></i> {{ title }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('users.users') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Users
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ title }}</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- Username -->
                        <div class="col-md-6 mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else ""), 
                                           placeholder="Enter username") }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Username must be 3-50 characters long</div>
                        </div>
                        
                        <!-- Email -->
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else ""), 
                                        placeholder="Enter email address") }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Full Name -->
                        <div class="col-md-6 mb-3">
                            {{ form.full_name.label(class="form-label") }}
                            {{ form.full_name(class="form-control" + (" is-invalid" if form.full_name.errors else ""), 
                                            placeholder="Enter full name (optional)") }}
                            {% if form.full_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.full_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Password -->
                        <div class="col-md-6 mb-3">
                            {{ form.password.label(class="form-label") }}
                            {% if user %}
                                <span class="text-muted">(Leave blank to keep current password)</span>
                            {% endif %}
                            <div class="input-group">
                                {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""), 
                                               placeholder="Enter password" + (" (optional)" if user else ""),
                                               id="password-input") }}
                                <button class="btn btn-outline-secondary" type="button" id="toggle-password">
                                    <i class="bi bi-eye" id="password-icon"></i>
                                </button>
                            </div>
                            {% if form.password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if not user %}
                                <div class="form-text">Password must be at least 6 characters long</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Organization Information -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-building"></i> Organization Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- Branch -->
                                <div class="col-md-6 mb-3">
                                    {{ form.branch.label(class="form-label") }}
                                    {{ form.branch(class="form-select" + (" is-invalid" if form.branch.errors else "")) }}
                                    {% if form.branch.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.branch.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Department -->
                                <div class="col-md-6 mb-3">
                                    {{ form.department.label(class="form-label") }}
                                    {{ form.department(class="form-select" + (" is-invalid" if form.department.errors else "")) }}
                                    {% if form.department.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.department.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="row">
                                <!-- Position -->
                                <div class="col-md-6 mb-3">
                                    {{ form.position.label(class="form-label") }}
                                    {{ form.position(class="form-select" + (" is-invalid" if form.position.errors else "")) }}
                                    {% if form.position.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.position.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Manager Name -->
                                <div class="col-md-6 mb-3">
                                    {{ form.manager_name.label(class="form-label") }}
                                    {{ form.manager_name(class="form-control" + (" is-invalid" if form.manager_name.errors else ""),
                                                        placeholder="Enter manager name (optional)") }}
                                    {% if form.manager_name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.manager_name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-person-lines-fill"></i> Additional Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- Bio -->
                                <div class="col-12 mb-3">
                                    {{ form.bio.label(class="form-label") }}
                                    {{ form.bio(class="form-control" + (" is-invalid" if form.bio.errors else ""),
                                               placeholder="Enter bio (optional)", rows="3") }}
                                    {% if form.bio.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.bio.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">Brief description about the user (max 500 characters)</div>
                                </div>
                            </div>

                            <div class="row">
                                <!-- Timezone -->
                                <div class="col-md-6 mb-3">
                                    {{ form.timezone.label(class="form-label") }}
                                    {{ form.timezone(class="form-control" + (" is-invalid" if form.timezone.errors else ""),
                                                   placeholder="e.g., UTC, America/New_York") }}
                                    {% if form.timezone.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.timezone.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Language -->
                                <div class="col-md-6 mb-3">
                                    {{ form.language.label(class="form-label") }}
                                    {{ form.language(class="form-control" + (" is-invalid" if form.language.errors else ""),
                                                   placeholder="e.g., en, es, fr") }}
                                    {% if form.language.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.language.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Status and Permissions -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-shield-check"></i> User Status & Permissions</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check form-switch">
                                        {{ form.is_active(class="form-check-input") }}
                                        {{ form.is_active.label(class="form-check-label") }}
                                    </div>
                                    <small class="text-muted">User can log in and access the system</small>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-check form-switch">
                                        {{ form.is_verified(class="form-check-input") }}
                                        {{ form.is_verified.label(class="form-check-label") }}
                                    </div>
                                    <small class="text-muted">Email address has been verified</small>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-check form-switch">
                                        {{ form.is_superuser(class="form-check-input") }}
                                        {{ form.is_superuser.label(class="form-check-label") }}
                                    </div>
                                    <small class="text-muted">Has administrative privileges</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-flex justify-content-end mt-4">
                        <a href="{{ url_for('users.users') }}" class="btn btn-outline-secondary me-2">
                            <i class="bi bi-x-circle"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-{% if user %}pencil{% else %}plus-circle{% endif %}"></i>
                            {{ 'Update User' if user else 'Create User' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

                    {% if profile_page %}
            <input type="hidden" name="profile_page" value="1">
    </div>
    
    <!-- User Information Sidebar -->
    <div class="col-lg-4">
        {% if user %}
        <!-- Existing User Info -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> User Information</h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-lg mx-auto mb-2">
                        <div class="avatar-title rounded-circle bg-primary text-white">
                            {{ user.username[0].upper() if user.username else 'U' }}
                        </div>
                    </div>
                    <h5 class="mb-1">{{ user.full_name or user.username }}</h5>
                    <p class="text-muted mb-0">{{ user.email }}</p>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-4">
                        <div class="mb-2">
                            {% if user.is_active %}
                                <i class="bi bi-check-circle text-success" style="font-size: 1.5rem;"></i>
                            {% else %}
                                <i class="bi bi-x-circle text-danger" style="font-size: 1.5rem;"></i>
                            {% endif %}
                        </div>
                        <small class="text-muted">Status</small>
                    </div>
                    <div class="col-4">
                        <div class="mb-2">
                            {% if user.is_verified %}
                                <i class="bi bi-patch-check text-success" style="font-size: 1.5rem;"></i>
                            {% else %}
                                <i class="bi bi-patch-exclamation text-warning" style="font-size: 1.5rem;"></i>
                            {% endif %}
                        </div>
                        <small class="text-muted">Verified</small>
                    </div>
                    <div class="col-4">
                        <div class="mb-2">
                            {% if user.is_superuser %}
                                <i class="bi bi-shield-check text-primary" style="font-size: 1.5rem;"></i>
                            {% else %}
                                <i class="bi bi-person text-secondary" style="font-size: 1.5rem;"></i>
                            {% endif %}
                        </div>
                        <small class="text-muted">Role</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="small">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">User ID:</span>
                        <span class="font-monospace">{{ user.id }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Created:</span>
                        <span>{{ user.created_at | datetime }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Last Login:</span>
                        <span>{{ user.last_login | datetime }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="text-muted">Login Count:</span>
                        <span>{{ user.login_count or 0 }}</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Danger Zone -->
        {% if user.id != session.user.id %}
        <div class="card border-danger mt-4">
            <div class="card-header bg-danger text-white">
                <h6 class="mb-0"><i class="bi bi-exclamation-triangle"></i> Danger Zone</h6>
            </div>
            <div class="card-body">
                <p class="text-muted small mb-3">
                    Permanently delete this user account. This action cannot be undone.
                </p>
                <form method="POST"  
                      onsubmit="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                    <button type="submit" class="btn btn-danger btn-sm">
                        <i class="bi bi-trash"></i> Delete User
                    </button>
                </form>
                {% if effective_permissions %}
                <div class="card mt-4">
                    <div class="card-header bg-light">
                        <i class="bi bi-shield-check"></i> Effective Permissions
                    </div>
                    <div class="card-body">
                        <ul class="mb-0 ps-3">
                            {% for perm in effective_permissions %}
                                <li title="{{ perm }}">{{ perm }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        {% else %}
        <!-- New User Guidelines -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-lightbulb"></i> User Creation Guidelines</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        Choose a unique username
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        Use a valid email address
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        Set a strong password (6+ characters)
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        Configure appropriate permissions
                    </li>
                    <li>
                        <i class="bi bi-check-circle text-success me-2"></i>
                        Verify email if needed
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-shield-check"></i> Permission Levels</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Regular User</strong>
                    <p class="text-muted small mb-0">Can access applications and manage their own profile</p>
                </div>
                <div>
                    <strong>Administrator</strong>
                    <p class="text-muted small mb-0">Full system access including user and application management</p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    // Password visibility toggle
    document.getElementById('toggle-password').addEventListener('click', function() {
        const passwordInput = document.getElementById('password-input');
        const passwordIcon = document.getElementById('password-icon');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            passwordIcon.className = 'bi bi-eye-slash';
        } else {
            passwordInput.type = 'password';
            passwordIcon.className = 'bi bi-eye';
        }
    });
    
    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const username = document.querySelector('[name="username"]').value;
        const email = document.querySelector('[name="email"]').value;
        const password = document.querySelector('[name="password"]').value;
        
        // Check if creating new user and password is empty
        {% if not user %}
        if (!password.trim()) {
            e.preventDefault();
            alert('Password is required for new users.');
            return;
        }
        {% endif %}
        
        // Basic email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            alert('Please enter a valid email address.');
            return;
        }
    });
</script>

<style>
    .avatar-lg {
        width: 80px;
        height: 80px;
    }
    
    .avatar-title {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 2rem;
    }
    
    .form-switch .form-check-input {
        width: 2.5rem;
        height: 1.25rem;
    }
    
    .card-header h6 {
        font-weight: 600;
    }
</style>
{% endblock %}