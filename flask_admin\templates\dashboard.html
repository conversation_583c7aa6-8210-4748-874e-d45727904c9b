{% extends "base.html" %} {% block title %}Dashboard - SSO Admin Panel{%
endblock %} {% block content %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
>
  <h1 class="h2"><i class="bi bi-speedometer2"></i> Admin Dashboard</h1>
  <div class="btn-toolbar mb-2 mb-md-0">
    <div class="btn-group me-2">
      <button
        type="button"
        class="btn btn-sm btn-outline-secondary"
        onclick="location.reload()"
      >
        <i class="bi bi-arrow-clockwise"></i> Refresh
      </button>
    </div>
  </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
  <div class="col-xl-3 col-md-6 mb-4">
    <div class="card stat-card h-100">
      <div class="card-body p-4">
        <div class="d-flex align-items-center justify-content-between">
          <div>
            <h6 class="text-uppercase fw-semibold text-muted mb-2 small">Total Users</h6>
            <h3 class="mb-1 fw-bold">{{ stats.system_stats.total_users if stats.system_stats else 0 }}</h3>
            <div class="small text-muted">All registered accounts</div>
          </div>
          <div class="icon-circle bg-light">
            <i class="bi bi-people text-primary"></i>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-xl-3 col-md-6 mb-4">
    <div class="card stat-card-success h-100">
      <div class="card-body p-4">
        <div class="d-flex align-items-center justify-content-between">
          <div>
            <h6 class="text-uppercase fw-semibold text-muted mb-2 small">Active Users</h6>
            <h3 class="mb-1 fw-bold">{{ stats.system_stats.active_users if stats.system_stats else 0 }}</h3>
            <div class="small text-muted">Currently active accounts</div>
          </div>
          <div class="icon-circle bg-light">
            <i class="bi bi-person-check text-success"></i>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-xl-3 col-md-6 mb-4">
    <div class="card stat-card-info h-100">
      <div class="card-body p-4">
        <div class="d-flex align-items-center justify-content-between">
          <div>
            <h6 class="text-uppercase fw-semibold text-muted mb-2 small">Applications</h6>
            <h3 class="mb-1 fw-bold">{{ stats.system_stats.total_applications if stats.system_stats else 0 }}</h3>
            <div class="small text-muted">Registered applications</div>
          </div>
          <div class="icon-circle bg-light">
            <i class="bi bi-app text-info"></i>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-xl-3 col-md-6 mb-4">
    <div class="card stat-card-warning h-100">
      <div class="card-body p-4">
        <div class="d-flex align-items-center justify-content-between">
          <div>
            <h6 class="text-uppercase fw-semibold text-muted mb-2 small">Failed Logins</h6>
            <h3 class="mb-1 fw-bold">{{ stats.system_stats.failed_logins_24h if stats.system_stats else 0 }}</h3>
            <div class="small text-muted">Last 24 hours</div>
          </div>
          <div class="icon-circle bg-light">
            <i class="bi bi-shield-exclamation text-warning"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Recent Activity and Quick Actions -->
<div class="row">
  <!-- Recent Activities -->
  <div class="col-lg-8 mb-4">
    <div class="card h-100">
      <div
        class="card-header d-flex justify-content-between align-items-center py-3"
      >
        <h5 class="mb-0 fw-semibold">
          <i class="bi bi-clock-history me-2"></i>Recent Activities
        </h5>
        <a href="#" class="btn btn-sm btn-outline-primary">
          <i class="bi bi-eye me-1"></i>View All
        </a>
      </div>
      <div class="card-body p-0">
        {% if stats.recent_activities %}
        <div class="list-group list-group-flush">
          {% for activity in stats.recent_activities[:10] %}
          <div
            class="list-group-item d-flex justify-content-between align-items-center py-3 px-4 border-start-0 border-end-0"
          >
            <div class="d-flex align-items-center">
              <div class="activity-icon me-3">
                {% if activity.type == 'user_registration' %}
                <div class="icon-circle bg-success-subtle">
                  <i class="bi bi-person-plus text-success"></i>
                </div>
                {% elif activity.type == 'application_created' %}
                <div class="icon-circle bg-info-subtle">
                  <i class="bi bi-app text-info"></i>
                </div>
                {% elif activity.type == 'user_login' %}
                <div class="icon-circle bg-primary-subtle">
                  <i class="bi bi-box-arrow-in-right text-primary"></i>
                </div>
                {% else %}
                <div class="icon-circle bg-secondary-subtle">
                  <i class="bi bi-activity text-secondary"></i>
                </div>
                {% endif %}
              </div>
              <div>
                <div class="fw-semibold">
                  {% if activity.type == 'user_registration' %}
                  New User Registration
                  {% elif activity.type == 'application_created' %}
                  New Application
                  {% elif activity.type == 'user_login' %}
                  User Login
                  {% else %}
                  Activity
                  {% endif %}
                </div>
                <div class="text-muted small">{{ activity.description }}</div>
              </div>
            </div>
            <div class="text-muted small badge bg-light text-dark">
              {{ activity.created_at.strftime('%Y-%m-%d %H:%M') if activity.created_at else 'Unknown' }}
            </div>
          </div>
          {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-5">
          <i class="bi bi-inbox" style="font-size: 3rem; color: var(--bs-gray-300)"></i>
          <p class="text-muted mt-3">No recent activities</p>
        </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="col-lg-4 mb-4">
    <div class="card h-100 mb-4">
      <div class="card-header py-3">
        <h5 class="mb-0 fw-semibold"><i class="bi bi-lightning me-2"></i>Quick Actions</h5>
      </div>
      <div class="card-body">
        <div class="d-grid gap-3">
          <a href="{{ url_for('users.edit_user', user_id=0) }}" class="btn btn-primary">
            <i class="bi bi-person-plus me-2"></i>Create New User
          </a>
          <a href="{{ url_for('users.users') }}" class="btn btn-outline-primary">
            <i class="bi bi-people me-2"></i>Manage Users
          </a>
          <a
            href="{{ url_for('applications.applications') }}"
            class="btn btn-outline-primary"
          >
            <i class="bi bi-app me-2"></i>Manage Applications
          </a>
          <button class="btn btn-outline-primary" onclick="generateReport()">
            <i class="bi bi-file-earmark-text me-2"></i>Generate Report
          </button>
        </div>
      </div>
    </div>

    <!-- System Status -->
    <div class="card mt-4">
      <div class="card-header py-3">
        <h5 class="mb-0 fw-semibold"><i class="bi bi-cpu me-2"></i>System Status</h5>
      </div>
      <div class="card-body">
        <div class="row text-center">
          <div class="col-6">
            <div class="border-end">
              <div class="status-icon text-success">
                <i class="bi bi-check-circle-fill"></i>
              </div>
              <div class="text-muted small mt-2">API Status</div>
            </div>
          </div>
          <div class="col-6">
            <div class="status-icon text-success">
              <i class="bi bi-database-check-fill"></i>
            </div>
            <div class="text-muted small mt-2">Database</div>
          </div>
        </div>
        <hr class="my-3" />
        <div class="text-center">
          <div class="text-muted small">
            <i class="bi bi-clock me-1"></i> Last updated: {{
            moment().format('YYYY-MM-DD HH:mm:ss') if moment else 'Just now' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- User Statistics Chart -->
{% if stats.user_stats %}
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h5 class="mb-0 fw-semibold">
          <i class="bi bi-graph-up me-2"></i>User Registration Trends
        </h5>
        <div class="btn-group">
          <button type="button" class="btn btn-sm btn-outline-primary active">Weekly</button>
          <button type="button" class="btn btn-sm btn-outline-primary">Monthly</button>
          <button type="button" class="btn btn-sm btn-outline-primary">Yearly</button>
        </div>
      </div>
      <div class="card-body p-4">
        <canvas
          id="userStatsChart"
          width="400"
          height="100"
          data-labels='{{ (stats.user_stats.registration_dates or []) | tojson | safe }}'
          data-counts='{{ (stats.user_stats.registration_counts or []) | tojson | safe }}'
        ></canvas>
      </div>
    </div>
  </div>
</div>
{% endif %} {% endblock %} {% block scripts %} {% if stats.user_stats %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  // User Registration Chart
  const canvas = document.getElementById('userStatsChart');
  if (canvas) {
    const ctx = canvas.getContext('2d');
    try {
      const labels = JSON.parse(canvas.dataset.labels || '[]');
      const counts = JSON.parse(canvas.dataset.counts || '[]');
      
      // Get primary color from CSS variables
      const primaryColor = getComputedStyle(document.documentElement)
        .getPropertyValue('--primary').trim() || '#4e73df';
      const primaryRgb = hexToRgb(primaryColor);
      
      const userStatsChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: labels,
          datasets: [
            {
              label: 'New Registrations',
              data: counts,
              borderColor: primaryColor,
              backgroundColor: `rgba(${primaryRgb.r}, ${primaryRgb.g}, ${primaryRgb.b}, 0.1)`,
              borderWidth: 2,
              tension: 0.4,
              fill: true,
              pointBackgroundColor: '#fff',
              pointBorderColor: primaryColor,
              pointBorderWidth: 2,
              pointRadius: 4,
              pointHoverRadius: 6,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              padding: 12,
              titleFont: {
                size: 14,
                weight: 'bold'
              },
              bodyFont: {
                size: 13
              },
              displayColors: false,
              callbacks: {
                title: function(tooltipItems) {
                  return tooltipItems[0].label;
                },
                label: function(context) {
                  return `New Users: ${context.raw}`;
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                stepSize: 1,
                font: {
                  size: 12
                },
                color: '#6c757d'
              },
              grid: {
                color: 'rgba(0, 0, 0, 0.05)',
                drawBorder: false
              }
            },
            x: {
              ticks: {
                font: {
                  size: 12
                },
                color: '#6c757d'
              },
              grid: {
                display: false,
                drawBorder: false
              }
            }
          },
          layout: {
            padding: {
              top: 10,
              right: 10,
              bottom: 10,
              left: 10
            }
          }
        },
      });
    } catch (e) {
      console.error('Failed to parse chart data:', e);
      const ctx = canvas.getContext('2d');
      ctx.font = '16px Arial';
      ctx.fillStyle = 'red';
      ctx.textAlign = 'center';
      ctx.fillText('Error: Could not load chart data.', canvas.width / 2, canvas.height / 2);
    }
  }
  
  // Helper function to convert hex to RGB
  function hexToRgb(hex) {
    // Remove # if present
    hex = hex.replace(/^#/, '');
    
    // Parse hex values
    let bigint = parseInt(hex, 16);
    let r = (bigint >> 16) & 255;
    let g = (bigint >> 8) & 255;
    let b = bigint & 255;
    
    return { r, g, b };
  }
</script>
{% endif %}

<script>
  // Function to generate a report
  function generateReport() {
    const toast = document.createElement('div');
    toast.className = 'toast position-fixed bottom-0 end-0 m-3';
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    toast.innerHTML = `
      <div class="toast-header bg-primary text-white">
        <i class="bi bi-file-earmark-text me-2"></i>
        <strong class="me-auto">Report Generation</strong>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
      <div class="toast-body">
        Your report is being generated. You will be notified when it's ready.
      </div>
    `;
    document.body.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Simulate report generation
    setTimeout(() => {
      toast.querySelector('.toast-header').classList.remove('bg-primary');
      toast.querySelector('.toast-header').classList.add('bg-success');
      toast.querySelector('.toast-header i').classList.remove('bi-file-earmark-text');
      toast.querySelector('.toast-header i').classList.add('bi-check-circle');
      toast.querySelector('.toast-header strong').textContent = 'Report Ready';
      toast.querySelector('.toast-body').innerHTML = 'Your report has been generated. <a href="#" class="text-decoration-none fw-semibold">Download now</a>';
      bsToast.show();
    }, 3000);
  }

  // Time period buttons functionality
  document.addEventListener('DOMContentLoaded', function() {
    const timePeriodButtons = document.querySelectorAll('.card-header .btn-group .btn');
    if (timePeriodButtons.length > 0) {
      timePeriodButtons.forEach(button => {
        button.addEventListener('click', function() {
          // Remove active class from all buttons
          timePeriodButtons.forEach(btn => btn.classList.remove('active'));
          // Add active class to clicked button
          this.classList.add('active');
          
          // In a real application, you would fetch new data based on the selected time period
          // and update the chart. For now, we'll just show a loading indicator
          const chartCanvas = document.getElementById('userStatsChart');
          if (chartCanvas) {
            const ctx = chartCanvas.getContext('2d');
            ctx.clearRect(0, 0, chartCanvas.width, chartCanvas.height);
            ctx.font = '16px Arial';
            ctx.fillStyle = '#6c757d';
            ctx.textAlign = 'center';
            ctx.fillText('Loading ' + this.textContent + ' data...', chartCanvas.width / 2, chartCanvas.height / 2);
            
            // Simulate data loading
            setTimeout(() => {
              // This would be replaced with actual data fetching and chart updating
              location.reload();
            }, 1500);
          }
        });
      });
    }
  });

  // Auto-refresh countdown
  let refreshTimeLeft = 300; // 5 minutes in seconds
  const refreshInterval = setInterval(function() {
    refreshTimeLeft -= 1;
    
    // Update refresh indicator if it exists
    const refreshIndicator = document.getElementById('refresh-indicator');
    if (refreshIndicator) {
      const minutes = Math.floor(refreshTimeLeft / 60);
      const seconds = refreshTimeLeft % 60;
      refreshIndicator.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
    }
    
    if (refreshTimeLeft <= 0) {
      clearInterval(refreshInterval);
      location.reload();
    }
  }, 1000);
  
  // Add refresh indicator to the page
  document.addEventListener('DOMContentLoaded', function() {
    const statusCard = document.querySelector('.system-status');
    if (statusCard) {
      const refreshDiv = document.createElement('div');
      refreshDiv.className = 'd-flex align-items-center mt-3 text-muted small';
      refreshDiv.innerHTML = `
        <i class="bi bi-arrow-clockwise me-2"></i>
        <span>Auto-refresh in <span id="refresh-indicator">5:00</span></span>
        <button class="btn btn-sm btn-link text-muted p-0 ms-2" onclick="location.reload()" title="Refresh now">
          <i class="bi bi-arrow-repeat"></i>
        </button>
      `;
      statusCard.querySelector('.card-body').appendChild(refreshDiv);
    }
  });
</script>
{% endblock %}
